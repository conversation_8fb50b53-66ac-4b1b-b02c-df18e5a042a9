[{"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js": "1", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js": "2", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js": "3", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js": "4", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js": "5", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js": "6", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx": "7", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx": "8", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx": "9", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx": "10", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx": "11", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx": "12", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx": "13", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx": "14", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx": "15", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx": "16", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx": "17", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx": "18", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx": "19", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx": "20", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx": "21", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx": "22", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx": "23", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js": "24", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx": "25", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx": "26", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx": "27", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx": "28", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx": "29", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx": "30", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx": "31", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx": "32", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx": "33", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx": "34", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx": "35", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx": "36", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx": "37", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx": "38", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx": "39", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx": "40", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx": "41", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx": "42", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx": "43", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx": "44", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx": "45", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx": "46", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx": "47", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx": "48", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\utils\\currency.js": "49", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\payboxContext.js": "50", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\PayboxPage.jsx": "51", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminPaybox.jsx": "52", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDeposit.jsx": "53", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxWallet.jsx": "54", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxTransactions.jsx": "55", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositForm.jsx": "56", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDebug.jsx": "57", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxSimpleTest.jsx": "58", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositSimple.jsx": "59"}, {"size": 649, "mtime": 1750943544495, "results": "60", "hashOfConfig": "61"}, {"size": 362, "mtime": 1750943544502, "results": "62", "hashOfConfig": "61"}, {"size": 5592, "mtime": 1751161601395, "results": "63", "hashOfConfig": "61"}, {"size": 1585, "mtime": 1750943544494, "results": "64", "hashOfConfig": "61"}, {"size": 4272, "mtime": 1751102620436, "results": "65", "hashOfConfig": "61"}, {"size": 4438, "mtime": 1750993170933, "results": "66", "hashOfConfig": "61"}, {"size": 2037, "mtime": 1751161219448, "results": "67", "hashOfConfig": "61"}, {"size": 3086, "mtime": 1750993154771, "results": "68", "hashOfConfig": "61"}, {"size": 562, "mtime": 1750943544488, "results": "69", "hashOfConfig": "61"}, {"size": 1986, "mtime": 1750996901833, "results": "70", "hashOfConfig": "61"}, {"size": 4057, "mtime": 1751102546590, "results": "71", "hashOfConfig": "61"}, {"size": 4611, "mtime": 1751101450925, "results": "72", "hashOfConfig": "61"}, {"size": 3166, "mtime": 1750943544501, "results": "73", "hashOfConfig": "61"}, {"size": 3374, "mtime": 1750943544501, "results": "74", "hashOfConfig": "61"}, {"size": 2746, "mtime": 1750943544500, "results": "75", "hashOfConfig": "61"}, {"size": 557, "mtime": 1750943544498, "results": "76", "hashOfConfig": "61"}, {"size": 1873, "mtime": 1750945277042, "results": "77", "hashOfConfig": "61"}, {"size": 5134, "mtime": 1751101343432, "results": "78", "hashOfConfig": "61"}, {"size": 3540, "mtime": 1751161277468, "results": "79", "hashOfConfig": "61"}, {"size": 10496, "mtime": 1751161487298, "results": "80", "hashOfConfig": "61"}, {"size": 4610, "mtime": 1750997032701, "results": "81", "hashOfConfig": "61"}, {"size": 392, "mtime": 1750943544489, "results": "82", "hashOfConfig": "61"}, {"size": 988, "mtime": 1750943544487, "results": "83", "hashOfConfig": "61"}, {"size": 1572, "mtime": 1751033967838, "results": "84", "hashOfConfig": "61"}, {"size": 1221, "mtime": 1750943544493, "results": "85", "hashOfConfig": "61"}, {"size": 228, "mtime": 1750943544490, "results": "86", "hashOfConfig": "61"}, {"size": 1156, "mtime": 1751101370404, "results": "87", "hashOfConfig": "61"}, {"size": 737, "mtime": 1750943544487, "results": "88", "hashOfConfig": "61"}, {"size": 956, "mtime": 1751102444680, "results": "89", "hashOfConfig": "61"}, {"size": 3665, "mtime": 1750943544493, "results": "90", "hashOfConfig": "61"}, {"size": 372, "mtime": 1750943544489, "results": "91", "hashOfConfig": "61"}, {"size": 2548, "mtime": 1750943544493, "results": "92", "hashOfConfig": "61"}, {"size": 2512, "mtime": 1751102495516, "results": "93", "hashOfConfig": "61"}, {"size": 1519, "mtime": 1750943544488, "results": "94", "hashOfConfig": "61"}, {"size": 639, "mtime": 1750943544492, "results": "95", "hashOfConfig": "61"}, {"size": 1826, "mtime": 1750943544491, "results": "96", "hashOfConfig": "61"}, {"size": 14091, "mtime": 1751103506356, "results": "97", "hashOfConfig": "61"}, {"size": 10577, "mtime": 1751102594379, "results": "98", "hashOfConfig": "61"}, {"size": 7625, "mtime": 1751031499765, "results": "99", "hashOfConfig": "61"}, {"size": 445, "mtime": 1750994572623, "results": "100", "hashOfConfig": "61"}, {"size": 3134, "mtime": 1750997224891, "results": "101", "hashOfConfig": "61"}, {"size": 3620, "mtime": 1751161642500, "results": "102", "hashOfConfig": "61"}, {"size": 8263, "mtime": 1751031465839, "results": "103", "hashOfConfig": "61"}, {"size": 551, "mtime": 1750993273555, "results": "104", "hashOfConfig": "61"}, {"size": 8081, "mtime": 1751031022948, "results": "105", "hashOfConfig": "61"}, {"size": 13462, "mtime": 1751035160109, "results": "106", "hashOfConfig": "61"}, {"size": 8445, "mtime": 1751031717046, "results": "107", "hashOfConfig": "61"}, {"size": 702, "mtime": 1750996844606, "results": "108", "hashOfConfig": "61"}, {"size": 1311, "mtime": 1751101310151, "results": "109", "hashOfConfig": "61"}, {"size": 6325, "mtime": 1751162836464, "results": "110", "hashOfConfig": "61"}, {"size": 6311, "mtime": 1751162692287, "results": "111", "hashOfConfig": "61"}, {"size": 10997, "mtime": 1751161540961, "results": "112", "hashOfConfig": "61"}, {"size": 6233, "mtime": 1751162586624, "results": "113", "hashOfConfig": "61"}, {"size": 2507, "mtime": 1751161030218, "results": "114", "hashOfConfig": "61"}, {"size": 5272, "mtime": 1751161099815, "results": "115", "hashOfConfig": "61"}, {"size": 2966, "mtime": 1751161076471, "results": "116", "hashOfConfig": "61"}, {"size": 2706, "mtime": 1751162015083, "results": "117", "hashOfConfig": "61"}, {"size": 3895, "mtime": 1751162852102, "results": "118", "hashOfConfig": "61"}, {"size": 5539, "mtime": 1751162670547, "results": "119", "hashOfConfig": "61"}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xy8mk5", {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "192", "usedDeprecatedRules": "193"}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js", ["299"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js", ["300"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js", ["301", "302", "303"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js", ["304", "305", "306", "307", "308", "309", "310", "311", "312"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx", ["313", "314"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx", ["315"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx", ["316", "317"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx", ["318", "319"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx", ["320", "321"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx", ["322"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx", ["323", "324"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx", ["325"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx", ["326"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx", ["327", "328", "329"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx", ["330", "331"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx", ["332", "333", "334", "335"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx", ["336", "337", "338", "339", "340", "341", "342", "343", "344"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js", ["345", "346"], [], "import axios from \"axios\";\n\n// Set base URL directly to Django backend\naxios.defaults.baseURL = 'http://localhost:8000';\n\n// Set default headers for JSON\naxios.defaults.headers.common['Content-Type'] = 'application/json';\naxios.defaults.headers.put['Content-Type'] = 'application/json';\naxios.defaults.headers.post['Content-Type'] = 'application/json';\naxios.defaults.headers.patch['Content-Type'] = 'application/json';\n\nfunction setJwt(jwt) {\n    if (jwt == undefined) {\n        delete axios.defaults.headers.common[\"Authorization\"];\n        return;\n    }\n    axios.defaults.headers.common[\"Authorization\"] = `JWT ${jwt}`;\n    console.log('JWT token set:', `JWT ${jwt.substring(0, 20)}...`);\n}\n\n// Add request interceptor to log requests\naxios.interceptors.request.use(\n    (config) => {\n        console.log('Making request:', config.method?.toUpperCase(), config.url);\n        console.log('Headers:', config.headers);\n        return config;\n    },\n    (error) => {\n        return Promise.reject(error);\n    }\n);\n\n// Add response interceptor to log responses\naxios.interceptors.response.use(\n    (response) => {\n        console.log('Response received:', response.status, response.config.url);\n        return response;\n    },\n    (error) => {\n        console.log('Request failed:', error.response?.status, error.config?.url);\n        console.log('Error details:', error.response?.data);\n        return Promise.reject(error);\n    }\n);\n\nexport default {\n    get:axios.get,\n    post:axios.post,\n    put:axios.put,\n    patch:axios.patch,\n    delete:axios.delete,\n    setJwt\n};", [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx", ["347"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx", ["348", "349"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx", ["350"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx", ["351", "352", "353"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx", ["354"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx", ["355"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\utils\\currency.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\payboxContext.js", ["356"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\PayboxPage.jsx", ["357", "358"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminPaybox.jsx", ["359"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDeposit.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxWallet.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxTransactions.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositForm.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDebug.jsx", ["360"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxSimpleTest.jsx", ["361"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositSimple.jsx", [], [], {"ruleId": "362", "severity": 1, "message": "363", "line": 44, "column": 3, "nodeType": "364", "endLine": 44, "endColumn": 12, "suggestions": "365"}, {"ruleId": "366", "severity": 1, "message": "367", "line": 35, "column": 49, "nodeType": "368", "messageId": "369", "endLine": 35, "endColumn": 51}, {"ruleId": "366", "severity": 1, "message": "367", "line": 63, "column": 18, "nodeType": "368", "messageId": "369", "endLine": 63, "endColumn": 20}, {"ruleId": "366", "severity": 1, "message": "367", "line": 69, "column": 15, "nodeType": "368", "messageId": "369", "endLine": 69, "endColumn": 17}, {"ruleId": "366", "severity": 1, "message": "367", "line": 127, "column": 45, "nodeType": "368", "messageId": "369", "endLine": 127, "endColumn": 47}, {"ruleId": "370", "severity": 1, "message": "371", "line": 58, "column": 15, "nodeType": "364", "messageId": "372", "endLine": 58, "endColumn": 19}, {"ruleId": "362", "severity": 1, "message": "373", "line": 104, "column": 6, "nodeType": "374", "endLine": 104, "endColumn": 8, "suggestions": "375"}, {"ruleId": "362", "severity": 1, "message": "376", "line": 116, "column": 6, "nodeType": "374", "endLine": 116, "endColumn": 18, "suggestions": "377"}, {"ruleId": "366", "severity": 1, "message": "378", "line": 121, "column": 20, "nodeType": "368", "messageId": "369", "endLine": 121, "endColumn": 22}, {"ruleId": "366", "severity": 1, "message": "378", "line": 121, "column": 53, "nodeType": "368", "messageId": "369", "endLine": 121, "endColumn": 55}, {"ruleId": "366", "severity": 1, "message": "378", "line": 125, "column": 17, "nodeType": "368", "messageId": "369", "endLine": 125, "endColumn": 19}, {"ruleId": "366", "severity": 1, "message": "378", "line": 125, "column": 44, "nodeType": "368", "messageId": "369", "endLine": 125, "endColumn": 46}, {"ruleId": "366", "severity": 1, "message": "378", "line": 129, "column": 20, "nodeType": "368", "messageId": "369", "endLine": 129, "endColumn": 22}, {"ruleId": "366", "severity": 1, "message": "367", "line": 133, "column": 16, "nodeType": "368", "messageId": "369", "endLine": 133, "endColumn": 18}, {"ruleId": "370", "severity": 1, "message": "379", "line": 13, "column": 23, "nodeType": "364", "messageId": "372", "endLine": 13, "endColumn": 38}, {"ruleId": "362", "severity": 1, "message": "380", "line": 27, "column": 6, "nodeType": "374", "endLine": 27, "endColumn": 8, "suggestions": "381"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 14, "column": 13, "nodeType": "384", "messageId": "385", "endLine": 14, "endColumn": 107, "fix": "386"}, {"ruleId": "362", "severity": 1, "message": "387", "line": 22, "column": 6, "nodeType": "374", "endLine": 22, "endColumn": 8, "suggestions": "388"}, {"ruleId": "366", "severity": 1, "message": "378", "line": 26, "column": 13, "nodeType": "368", "messageId": "369", "endLine": 26, "endColumn": 15}, {"ruleId": "370", "severity": 1, "message": "389", "line": 1, "column": 29, "nodeType": "364", "messageId": "372", "endLine": 1, "endColumn": 38}, {"ruleId": "366", "severity": 1, "message": "378", "line": 26, "column": 13, "nodeType": "368", "messageId": "369", "endLine": 26, "endColumn": 15}, {"ruleId": "362", "severity": 1, "message": "390", "line": 35, "column": 6, "nodeType": "374", "endLine": 35, "endColumn": 8, "suggestions": "391"}, {"ruleId": "366", "severity": 1, "message": "378", "line": 44, "column": 13, "nodeType": "368", "messageId": "369", "endLine": 44, "endColumn": 15}, {"ruleId": "362", "severity": 1, "message": "392", "line": 31, "column": 6, "nodeType": "374", "endLine": 31, "endColumn": 8, "suggestions": "393"}, {"ruleId": "394", "severity": 1, "message": "395", "line": 18, "column": 28, "nodeType": "364", "messageId": "396", "endLine": 18, "endColumn": 36}, {"ruleId": "362", "severity": 1, "message": "380", "line": 22, "column": 6, "nodeType": "374", "endLine": 22, "endColumn": 8, "suggestions": "397"}, {"ruleId": "362", "severity": 1, "message": "392", "line": 22, "column": 6, "nodeType": "374", "endLine": 22, "endColumn": 8, "suggestions": "398"}, {"ruleId": "362", "severity": 1, "message": "399", "line": 15, "column": 6, "nodeType": "374", "endLine": 15, "endColumn": 8, "suggestions": "400"}, {"ruleId": "370", "severity": 1, "message": "401", "line": 30, "column": 11, "nodeType": "364", "messageId": "372", "endLine": 30, "endColumn": 13}, {"ruleId": "366", "severity": 1, "message": "367", "line": 60, "column": 38, "nodeType": "368", "messageId": "369", "endLine": 60, "endColumn": 40}, {"ruleId": "366", "severity": 1, "message": "367", "line": 129, "column": 53, "nodeType": "368", "messageId": "369", "endLine": 129, "endColumn": 55}, {"ruleId": "366", "severity": 1, "message": "367", "line": 39, "column": 33, "nodeType": "368", "messageId": "369", "endLine": 39, "endColumn": 35}, {"ruleId": "366", "severity": 1, "message": "367", "line": 60, "column": 33, "nodeType": "368", "messageId": "369", "endLine": 60, "endColumn": 35}, {"ruleId": "366", "severity": 1, "message": "367", "line": 31, "column": 47, "nodeType": "368", "messageId": "369", "endLine": 31, "endColumn": 49}, {"ruleId": "366", "severity": 1, "message": "367", "line": 32, "column": 47, "nodeType": "368", "messageId": "369", "endLine": 32, "endColumn": 49}, {"ruleId": "362", "severity": 1, "message": "402", "line": 39, "column": 6, "nodeType": "374", "endLine": 39, "endColumn": 8, "suggestions": "403"}, {"ruleId": "366", "severity": 1, "message": "378", "line": 65, "column": 17, "nodeType": "368", "messageId": "369", "endLine": 65, "endColumn": 19}, {"ruleId": "370", "severity": 1, "message": "379", "line": 18, "column": 24, "nodeType": "364", "messageId": "372", "endLine": 18, "endColumn": 39}, {"ruleId": "362", "severity": 1, "message": "404", "line": 43, "column": 6, "nodeType": "374", "endLine": 43, "endColumn": 8, "suggestions": "405"}, {"ruleId": "366", "severity": 1, "message": "378", "line": 49, "column": 21, "nodeType": "368", "messageId": "369", "endLine": 49, "endColumn": 23}, {"ruleId": "366", "severity": 1, "message": "367", "line": 51, "column": 34, "nodeType": "368", "messageId": "369", "endLine": 51, "endColumn": 36}, {"ruleId": "366", "severity": 1, "message": "378", "line": 55, "column": 24, "nodeType": "368", "messageId": "369", "endLine": 55, "endColumn": 26}, {"ruleId": "366", "severity": 1, "message": "367", "line": 57, "column": 37, "nodeType": "368", "messageId": "369", "endLine": 57, "endColumn": 39}, {"ruleId": "366", "severity": 1, "message": "378", "line": 63, "column": 13, "nodeType": "368", "messageId": "369", "endLine": 63, "endColumn": 15}, {"ruleId": "366", "severity": 1, "message": "378", "line": 63, "column": 35, "nodeType": "368", "messageId": "369", "endLine": 63, "endColumn": 37}, {"ruleId": "366", "severity": 1, "message": "378", "line": 66, "column": 20, "nodeType": "368", "messageId": "369", "endLine": 66, "endColumn": 22}, {"ruleId": "366", "severity": 1, "message": "367", "line": 13, "column": 13, "nodeType": "368", "messageId": "369", "endLine": 13, "endColumn": 15}, {"ruleId": "406", "severity": 1, "message": "407", "line": 46, "column": 1, "nodeType": "408", "endLine": 53, "endColumn": 3}, {"ruleId": "370", "severity": 1, "message": "389", "line": 1, "column": 17, "nodeType": "364", "messageId": "372", "endLine": 1, "endColumn": 26}, {"ruleId": "366", "severity": 1, "message": "367", "line": 90, "column": 34, "nodeType": "368", "messageId": "369", "endLine": 90, "endColumn": 36}, {"ruleId": "366", "severity": 1, "message": "367", "line": 90, "column": 51, "nodeType": "368", "messageId": "369", "endLine": 90, "endColumn": 53}, {"ruleId": "366", "severity": 1, "message": "367", "line": 22, "column": 47, "nodeType": "368", "messageId": "369", "endLine": 22, "endColumn": 49}, {"ruleId": "370", "severity": 1, "message": "389", "line": 1, "column": 17, "nodeType": "364", "messageId": "372", "endLine": 1, "endColumn": 26}, {"ruleId": "370", "severity": 1, "message": "409", "line": 8, "column": 8, "nodeType": "364", "messageId": "372", "endLine": 8, "endColumn": 19}, {"ruleId": "370", "severity": 1, "message": "410", "line": 14, "column": 10, "nodeType": "364", "messageId": "372", "endLine": 14, "endColumn": 15}, {"ruleId": "370", "severity": 1, "message": "411", "line": 9, "column": 10, "nodeType": "364", "messageId": "372", "endLine": 9, "endColumn": 18}, {"ruleId": "370", "severity": 1, "message": "412", "line": 1, "column": 8, "nodeType": "364", "messageId": "372", "endLine": 1, "endColumn": 13}, {"ruleId": "362", "severity": 1, "message": "413", "line": 183, "column": 6, "nodeType": "374", "endLine": 183, "endColumn": 41, "suggestions": "414"}, {"ruleId": "370", "severity": 1, "message": "415", "line": 7, "column": 8, "nodeType": "364", "messageId": "372", "endLine": 7, "endColumn": 21}, {"ruleId": "370", "severity": 1, "message": "416", "line": 18, "column": 11, "nodeType": "364", "messageId": "372", "endLine": 18, "endColumn": 17}, {"ruleId": "370", "severity": 1, "message": "417", "line": 2, "column": 65, "nodeType": "364", "messageId": "372", "endLine": 2, "endColumn": 70}, {"ruleId": "370", "severity": 1, "message": "389", "line": 1, "column": 29, "nodeType": "364", "messageId": "372", "endLine": 1, "endColumn": 38}, {"ruleId": "370", "severity": 1, "message": "409", "line": 4, "column": 8, "nodeType": "364", "messageId": "372", "endLine": 4, "endColumn": 19}, "react-hooks/exhaustive-deps", "React Hook useEffect contains a call to 'setKeyword'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [keywordParam] as a second argument to the useEffect Hook.", "Identifier", ["418"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "no-unused-vars", "'data' is assigned a value but never used.", "unusedVar", "React Hook useEffect has missing dependencies: 'authTokens' and 'refresh'. Either include them or remove the dependency array.", "ArrayExpression", ["419"], "React Hook useEffect has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["420"], "Expected '!==' and instead saw '!='.", "'setSearchParams' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'navigate', 'redirect', and 'userInfo'. Either include them or remove the dependency array.", ["421"], "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "JSXOpeningElement", "noTargetBlankWithoutNoreferrer", {"range": "422", "text": "423"}, "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["424"], "'useEffect' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'loadProduct'. Either include them or remove the dependency array.", ["425"], "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo'. Either include them or remove the dependency array.", ["426"], "no-const-assign", "'redirect' is constant.", "const", ["427"], ["428"], "React Hook useEffect has missing dependencies: 'logout', 'navigate', and 'userInfo'. Either include them or remove the dependency array.", ["429"], "'id' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'logout'. Either include them or remove the dependency array.", ["430"], "React Hook useEffect has missing dependencies: 'brandParam', 'categoryParam', and 'loadProducts'. Either include them or remove the dependency array.", ["431"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'httpService' is defined but never used.", "'email' is assigned a value but never used.", "'products' is assigned a value but never used.", "'React' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchTransactions' and 'fetchWallet'. Either include them or remove the dependency array.", ["432"], "'PayboxDeposit' is defined but never used.", "'wallet' is assigned a value but never used.", "'Alert' is defined but never used.", {"desc": "433", "fix": "434"}, {"desc": "435", "fix": "436"}, {"desc": "435", "fix": "437"}, {"desc": "438", "fix": "439"}, [447, 447], " rel=\"noreferrer\"", {"desc": "440", "fix": "441"}, {"desc": "442", "fix": "443"}, {"desc": "444", "fix": "445"}, {"desc": "438", "fix": "446"}, {"desc": "444", "fix": "447"}, {"desc": "448", "fix": "449"}, {"desc": "450", "fix": "451"}, {"desc": "452", "fix": "453"}, {"desc": "454", "fix": "455"}, "Add dependencies array: [keywordParam]", {"range": "456", "text": "457"}, "Update the dependencies array to be: [authTokens, refresh]", {"range": "458", "text": "459"}, {"range": "460", "text": "459"}, "Update the dependencies array to be: [navigate, redirect, userInfo]", {"range": "461", "text": "462"}, "Update the dependencies array to be: [loadProducts]", {"range": "463", "text": "464"}, "Update the dependencies array to be: [id, loadProduct]", {"range": "465", "text": "466"}, "Update the dependencies array to be: [navigate, userInfo]", {"range": "467", "text": "468"}, {"range": "469", "text": "462"}, {"range": "470", "text": "468"}, "Update the dependencies array to be: [logout, navigate, userInfo]", {"range": "471", "text": "472"}, "Update the dependencies array to be: [id, logout]", {"range": "473", "text": "474"}, "Update the dependencies array to be: [brandParam, categoryParam, loadProducts]", {"range": "475", "text": "476"}, "Update the dependencies array to be: [userInfo, authTokens, userLoading, fetchWallet, fetchTransactions]", {"range": "477", "text": "478"}, [1990, 1990], ", [keywordParam]", [2796, 2798], "[authTokens, refresh]", [3172, 3184], [956, 958], "[navigate, redirect, userInfo]", [839, 841], "[loadProducts]", [1058, 1060], "[id, loadProduct]", [1168, 1170], "[navigate, userInfo]", [833, 835], [858, 860], [491, 493], "[logout, navigate, userInfo]", [1634, 1636], "[id, logout]", [1488, 1490], "[brandParam, categoryParam, loadProducts]", [5724, 5759], "[userInfo, authTokens, userLoading, fetchWallet, fetchTransactions]"]