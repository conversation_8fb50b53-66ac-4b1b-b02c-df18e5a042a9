{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\PayboxSimpleTest.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext } from \"react\";\nimport { Card, Button, Alert } from \"react-bootstrap\";\nimport UserContext from \"../context/userContext\";\nimport httpService from \"../services/httpService\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PayboxSimpleTest() {\n  _s();\n  const [result, setResult] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const {\n    userInfo,\n    authTokens\n  } = useContext(UserContext);\n  const testWalletAPI = async () => {\n    setLoading(true);\n    setResult(\"\");\n    try {\n      console.log(\"=== SIMPLE WALLET TEST ===\");\n      console.log(\"User:\", userInfo);\n      console.log(\"Auth tokens present:\", !!authTokens);\n      const response = await fetch(\"http://localhost:8000/api/paybox/wallet/\", {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `JWT ${authTokens === null || authTokens === void 0 ? void 0 : authTokens.access}`\n        }\n      });\n      console.log(\"Response status:\", response.status);\n      console.log(\"Response headers:\", response.headers);\n      const data = await response.json();\n      console.log(\"Response data:\", data);\n      if (response.ok) {\n        setResult(`✅ Success: ${JSON.stringify(data, null, 2)}`);\n      } else {\n        setResult(`❌ Error ${response.status}: ${JSON.stringify(data, null, 2)}`);\n      }\n    } catch (error) {\n      console.error(\"Fetch error:\", error);\n      setResult(`❌ Network Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const testTransactionsAPI = async () => {\n    setLoading(true);\n    setResult(\"\");\n    try {\n      console.log(\"=== SIMPLE TRANSACTIONS TEST ===\");\n      const response = await fetch(\"http://localhost:8000/api/paybox/transactions/\", {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `JWT ${authTokens === null || authTokens === void 0 ? void 0 : authTokens.access}`\n        }\n      });\n      console.log(\"Response status:\", response.status);\n      const data = await response.json();\n      console.log(\"Response data:\", data);\n      if (response.ok) {\n        setResult(`✅ Transactions Success: ${JSON.stringify(data, null, 2)}`);\n      } else {\n        setResult(`❌ Transactions Error ${response.status}: ${JSON.stringify(data, null, 2)}`);\n      }\n    } catch (error) {\n      console.error(\"Fetch error:\", error);\n      setResult(`❌ Network Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"mb-3\",\n    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n      children: /*#__PURE__*/_jsxDEV(\"h6\", {\n        className: \"mb-0\",\n        children: \"\\uD83E\\uDDEA Simple API Test\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"User:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), \" \", (userInfo === null || userInfo === void 0 ? void 0 : userInfo.username) || \"Not logged in\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Auth Token:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), \" \", authTokens !== null && authTokens !== void 0 && authTokens.access ? \"Present\" : \"Missing\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: testWalletAPI,\n          disabled: loading,\n          className: \"me-2\",\n          children: loading ? \"Testing...\" : \"Test Wallet API\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: testTransactionsAPI,\n          disabled: loading,\n          className: \"me-2\",\n          children: loading ? \"Testing...\" : \"Test Transactions API\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"info\",\n          onClick: () => window.location.reload(),\n          size: \"sm\",\n          children: \"Refresh Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), result && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: result.includes(\"✅\") ? \"success\" : \"danger\",\n        children: /*#__PURE__*/_jsxDEV(\"pre\", {\n          className: \"small mb-0\",\n          style: {\n            whiteSpace: \"pre-wrap\"\n          },\n          children: result\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n}\n_s(PayboxSimpleTest, \"bpRXiX2EtBHEqCyVw1NVO4lMaUc=\");\n_c = PayboxSimpleTest;\nexport default PayboxSimpleTest;\nvar _c;\n$RefreshReg$(_c, \"PayboxSimpleTest\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "Card", "<PERSON><PERSON>", "<PERSON><PERSON>", "UserContext", "httpService", "jsxDEV", "_jsxDEV", "PayboxSimpleTest", "_s", "result", "setResult", "loading", "setLoading", "userInfo", "authTokens", "testWalletAPI", "console", "log", "response", "fetch", "method", "headers", "access", "status", "data", "json", "ok", "JSON", "stringify", "error", "message", "testTransactionsAPI", "className", "children", "Header", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "username", "variant", "onClick", "disabled", "window", "location", "reload", "size", "includes", "style", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/PayboxSimpleTest.jsx"], "sourcesContent": ["import React, { useState, useContext } from \"react\";\nimport { <PERSON>, <PERSON>, Alert } from \"react-bootstrap\";\nimport UserContext from \"../context/userContext\";\nimport httpService from \"../services/httpService\";\n\nfunction PayboxSimpleTest() {\n  const [result, setResult] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const { userInfo, authTokens } = useContext(UserContext);\n\n  const testWalletAPI = async () => {\n    setLoading(true);\n    setResult(\"\");\n    \n    try {\n      console.log(\"=== SIMPLE WALLET TEST ===\");\n      console.log(\"User:\", userInfo);\n      console.log(\"Auth tokens present:\", !!authTokens);\n      \n      const response = await fetch(\"http://localhost:8000/api/paybox/wallet/\", {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `JWT ${authTokens?.access}`\n        }\n      });\n      \n      console.log(\"Response status:\", response.status);\n      console.log(\"Response headers:\", response.headers);\n      \n      const data = await response.json();\n      console.log(\"Response data:\", data);\n      \n      if (response.ok) {\n        setResult(`✅ Success: ${JSON.stringify(data, null, 2)}`);\n      } else {\n        setResult(`❌ Error ${response.status}: ${JSON.stringify(data, null, 2)}`);\n      }\n    } catch (error) {\n      console.error(\"Fetch error:\", error);\n      setResult(`❌ Network Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testTransactionsAPI = async () => {\n    setLoading(true);\n    setResult(\"\");\n    \n    try {\n      console.log(\"=== SIMPLE TRANSACTIONS TEST ===\");\n      \n      const response = await fetch(\"http://localhost:8000/api/paybox/transactions/\", {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `JWT ${authTokens?.access}`\n        }\n      });\n      \n      console.log(\"Response status:\", response.status);\n      const data = await response.json();\n      console.log(\"Response data:\", data);\n      \n      if (response.ok) {\n        setResult(`✅ Transactions Success: ${JSON.stringify(data, null, 2)}`);\n      } else {\n        setResult(`❌ Transactions Error ${response.status}: ${JSON.stringify(data, null, 2)}`);\n      }\n    } catch (error) {\n      console.error(\"Fetch error:\", error);\n      setResult(`❌ Network Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Card className=\"mb-3\">\n      <Card.Header>\n        <h6 className=\"mb-0\">🧪 Simple API Test</h6>\n      </Card.Header>\n      <Card.Body>\n        <div className=\"mb-3\">\n          <strong>User:</strong> {userInfo?.username || \"Not logged in\"}\n        </div>\n        \n        <div className=\"mb-3\">\n          <strong>Auth Token:</strong> {authTokens?.access ? \"Present\" : \"Missing\"}\n        </div>\n        \n        <div className=\"mb-3\">\n          <Button\n            variant=\"primary\"\n            onClick={testWalletAPI}\n            disabled={loading}\n            className=\"me-2\"\n          >\n            {loading ? \"Testing...\" : \"Test Wallet API\"}\n          </Button>\n\n          <Button\n            variant=\"secondary\"\n            onClick={testTransactionsAPI}\n            disabled={loading}\n            className=\"me-2\"\n          >\n            {loading ? \"Testing...\" : \"Test Transactions API\"}\n          </Button>\n\n          <Button\n            variant=\"info\"\n            onClick={() => window.location.reload()}\n            size=\"sm\"\n          >\n            Refresh Page\n          </Button>\n        </div>\n        \n        {result && (\n          <Alert variant={result.includes(\"✅\") ? \"success\" : \"danger\"}>\n            <pre className=\"small mb-0\" style={{whiteSpace: \"pre-wrap\"}}>\n              {result}\n            </pre>\n          </Alert>\n        )}\n      </Card.Body>\n    </Card>\n  );\n}\n\nexport default PayboxSimpleTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AACnD,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,QAAQ,iBAAiB;AACrD,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EAC1B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEe,QAAQ;IAAEC;EAAW,CAAC,GAAGf,UAAU,CAACI,WAAW,CAAC;EAExD,MAAMY,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCH,UAAU,CAAC,IAAI,CAAC;IAChBF,SAAS,CAAC,EAAE,CAAC;IAEb,IAAI;MACFM,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzCD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEJ,QAAQ,CAAC;MAC9BG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,CAAC,CAACH,UAAU,CAAC;MAEjD,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,0CAA0C,EAAE;QACvEC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,OAAMP,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEQ,MAAO;QAC7C;MACF,CAAC,CAAC;MAEFN,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,QAAQ,CAACK,MAAM,CAAC;MAChDP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,QAAQ,CAACG,OAAO,CAAC;MAElD,MAAMG,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,EAAE;MAClCT,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEO,IAAI,CAAC;MAEnC,IAAIN,QAAQ,CAACQ,EAAE,EAAE;QACfhB,SAAS,CAAE,cAAaiB,IAAI,CAACC,SAAS,CAACJ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAE,EAAC,CAAC;MAC1D,CAAC,MAAM;QACLd,SAAS,CAAE,WAAUQ,QAAQ,CAACK,MAAO,KAAII,IAAI,CAACC,SAAS,CAACJ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAE,EAAC,CAAC;MAC3E;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCnB,SAAS,CAAE,oBAAmBmB,KAAK,CAACC,OAAQ,EAAC,CAAC;IAChD,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtCnB,UAAU,CAAC,IAAI,CAAC;IAChBF,SAAS,CAAC,EAAE,CAAC;IAEb,IAAI;MACFM,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAE/C,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,gDAAgD,EAAE;QAC7EC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,OAAMP,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEQ,MAAO;QAC7C;MACF,CAAC,CAAC;MAEFN,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,QAAQ,CAACK,MAAM,CAAC;MAChD,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,EAAE;MAClCT,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEO,IAAI,CAAC;MAEnC,IAAIN,QAAQ,CAACQ,EAAE,EAAE;QACfhB,SAAS,CAAE,2BAA0BiB,IAAI,CAACC,SAAS,CAACJ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAE,EAAC,CAAC;MACvE,CAAC,MAAM;QACLd,SAAS,CAAE,wBAAuBQ,QAAQ,CAACK,MAAO,KAAII,IAAI,CAACC,SAAS,CAACJ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAE,EAAC,CAAC;MACxF;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCnB,SAAS,CAAE,oBAAmBmB,KAAK,CAACC,OAAQ,EAAC,CAAC;IAChD,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEN,OAAA,CAACN,IAAI;IAACgC,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACpB3B,OAAA,CAACN,IAAI,CAACkC,MAAM;MAAAD,QAAA,eACV3B,OAAA;QAAI0B,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAkB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAK;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAChC,eACdhC,OAAA,CAACN,IAAI,CAACuC,IAAI;MAAAN,QAAA,gBACR3B,OAAA;QAAK0B,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3B,OAAA;UAAA2B,QAAA,EAAQ;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS,KAAC,EAAC,CAAAzB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE2B,QAAQ,KAAI,eAAe;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACzD,eAENhC,OAAA;QAAK0B,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3B,OAAA;UAAA2B,QAAA,EAAQ;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS,KAAC,EAACxB,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEQ,MAAM,GAAG,SAAS,GAAG,SAAS;MAAA;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACpE,eAENhC,OAAA;QAAK0B,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3B,OAAA,CAACL,MAAM;UACLwC,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAE3B,aAAc;UACvB4B,QAAQ,EAAEhC,OAAQ;UAClBqB,SAAS,EAAC,MAAM;UAAAC,QAAA,EAEftB,OAAO,GAAG,YAAY,GAAG;QAAiB;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACpC,eAEThC,OAAA,CAACL,MAAM;UACLwC,OAAO,EAAC,WAAW;UACnBC,OAAO,EAAEX,mBAAoB;UAC7BY,QAAQ,EAAEhC,OAAQ;UAClBqB,SAAS,EAAC,MAAM;UAAAC,QAAA,EAEftB,OAAO,GAAG,YAAY,GAAG;QAAuB;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAC1C,eAEThC,OAAA,CAACL,MAAM;UACLwC,OAAO,EAAC,MAAM;UACdC,OAAO,EAAEA,CAAA,KAAME,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAG;UACxCC,IAAI,EAAC,IAAI;UAAAd,QAAA,EACV;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACL,EAEL7B,MAAM,iBACLH,OAAA,CAACJ,KAAK;QAACuC,OAAO,EAAEhC,MAAM,CAACuC,QAAQ,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,QAAS;QAAAf,QAAA,eAC1D3B,OAAA;UAAK0B,SAAS,EAAC,YAAY;UAACiB,KAAK,EAAE;YAACC,UAAU,EAAE;UAAU,CAAE;UAAAjB,QAAA,EACzDxB;QAAM;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAET;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACS;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACP;AAEX;AAAC9B,EAAA,CA7HQD,gBAAgB;AAAA4C,EAAA,GAAhB5C,gBAAgB;AA+HzB,eAAeA,gBAAgB;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}