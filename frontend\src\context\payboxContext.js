import React, { createContext, useState, useContext, useEffect } from "react";
import httpService from "../services/httpService";
import UserContext from "./userContext";

const PayboxContext = createContext();

export const PayboxProvider = ({ children }) => {
  const [wallet, setWallet] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const { userInfo } = useContext(UserContext);

  // Lấy thông tin ví
  const fetchWallet = async () => {
    if (!userInfo) return;
    
    try {
      setLoading(true);
      const { data } = await httpService.get("/api/paybox/wallet/");
      setWallet(data);
      setError("");
    } catch (ex) {
      setError("Không thể tải thông tin ví");
      console.error("Error fetching wallet:", ex);
    } finally {
      setLoading(false);
    }
  };

  // L<PERSON>y lịch sử giao dịch
  const fetchTransactions = async () => {
    if (!userInfo) return;
    
    try {
      setLoading(true);
      const { data } = await httpService.get("/api/paybox/transactions/");
      setTransactions(data);
      setError("");
    } catch (ex) {
      setError("Không thể tải lịch sử giao dịch");
      console.error("Error fetching transactions:", ex);
    } finally {
      setLoading(false);
    }
  };

  // Tạo payment intent để nạp tiền
  const createDepositIntent = async (amount) => {
    try {
      setLoading(true);
      const { data } = await httpService.post("/api/paybox/deposit/", {
        amount: amount
      });
      setError("");
      return data;
    } catch (ex) {
      setError("Không thể tạo yêu cầu nạp tiền");
      console.error("Error creating deposit intent:", ex);
      throw ex;
    } finally {
      setLoading(false);
    }
  };

  // Xác nhận nạp tiền thành công
  const confirmDeposit = async (paymentIntentId) => {
    try {
      setLoading(true);
      const { data } = await httpService.post("/api/paybox/deposit/confirm/", {
        payment_intent_id: paymentIntentId
      });
      
      // Cập nhật lại thông tin ví và giao dịch
      await fetchWallet();
      await fetchTransactions();
      
      setError("");
      return data;
    } catch (ex) {
      setError("Không thể xác nhận nạp tiền");
      console.error("Error confirming deposit:", ex);
      throw ex;
    } finally {
      setLoading(false);
    }
  };

  // Thanh toán đơn hàng bằng ví
  const payWithPaybox = async (orderId) => {
    try {
      setLoading(true);
      const { data } = await httpService.post("/api/paybox/payment/", {
        order_id: orderId
      });
      
      // Cập nhật lại thông tin ví và giao dịch
      await fetchWallet();
      await fetchTransactions();
      
      setError("");
      return data;
    } catch (ex) {
      const errorMessage = ex.response?.data?.error || "Không thể thanh toán bằng ví";
      setError(errorMessage);
      console.error("Error paying with paybox:", ex);
      throw ex;
    } finally {
      setLoading(false);
    }
  };

  // Format số tiền VND
  const formatVND = (amount) => {
    return new Intl.NumberFormat('vi-VN').format(amount) + ' VND';
  };

  // Kiểm tra số dư có đủ không
  const hasSufficientBalance = (amount) => {
    return wallet && wallet.balance >= amount;
  };

  // Load dữ liệu khi user đăng nhập
  useEffect(() => {
    if (userInfo) {
      fetchWallet();
      fetchTransactions();
    } else {
      setWallet(null);
      setTransactions([]);
    }
  }, [userInfo]);

  const contextData = {
    wallet,
    transactions,
    loading,
    error,
    fetchWallet,
    fetchTransactions,
    createDepositIntent,
    confirmDeposit,
    payWithPaybox,
    formatVND,
    hasSufficientBalance,
    setError
  };

  return (
    <PayboxContext.Provider value={contextData}>
      {children}
    </PayboxContext.Provider>
  );
};

export default PayboxContext;
